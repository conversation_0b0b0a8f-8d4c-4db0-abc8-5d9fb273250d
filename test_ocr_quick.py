#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR快速测试应用
用于快速测试OCR功能，无需运行完整的自动化流程
"""

import os
import sys
import time
import numpy as np
import pandas as pd
import pyautogui
import cv2
from datetime import datetime
from paddleocr import PaddleOCR
import warnings

# 忽略特定的警告
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*No ccache found.*")

# OCR全局变量
ocr_instance = None

def get_ocr_instance():
    """获取OCR实例（单例模式）"""
    global ocr_instance
    if ocr_instance is None:
        print("正在初始化PaddleOCR...")
        ocr_instance = PaddleOCR(
            lang="ch",  # 中文模型
            device="cpu",  # 使用CPU推理
            use_doc_orientation_classify=False,  # 关闭文档方向分类
            use_doc_unwarping=False,  # 关闭文档形变校正
            use_textline_orientation=False,  # 关闭文本行方向分类
            text_det_limit_side_len=960,  # 限制最长边为960
            text_det_limit_type="max",  # 限制类型为最大边
            text_det_box_thresh=0.5,  # 检测框阈值
            text_det_thresh=0.3,  # 检测阈值
            text_det_unclip_ratio=2.0,  # 文本框扩张比例
            text_rec_score_thresh=0.3,  # 识别阈值
            enable_mkldnn=True,  # 启用mkldnn加速
            cpu_threads=10  # CPU线程数
        )
        print("PaddleOCR初始化完成")
    return ocr_instance

def log_message(message):
    """打印带时间戳的日志"""
    timestamp = datetime.now().strftime('%H:%M:%S')
    print(f"[{timestamp}] {message}")

def capture_and_ocr_screen(x1, y1, x2, y2, product_index):
    """
    截取指定区域的屏幕并进行OCR识别，同时保存截图
    
    Args:
        x1, y1: 左上角坐标
        x2, y2: 右下角坐标
        product_index: 商品编号
        
    Returns:
        tuple: (OCR识别结果列表, 截图保存路径)
    """
    try:
        log_message(f"开始OCR识别屏幕区域: ({x1}, {y1}) 到 ({x2}, {y2})")
        
        # 截取屏幕指定区域
        screenshot = pyautogui.screenshot(region=(x1, y1, x2-x1, y2-y1))
        
        # 确保商品图片文件夹存在
        goods_folder = "商品图片"
        os.makedirs(goods_folder, exist_ok=True)

        # 保存OCR截图到商品图片文件夹（直接保存在商品图片目录下）
        screenshot_filename = f"{product_index}.jpg"
        screenshot_path = os.path.join(goods_folder, screenshot_filename)
        
        # 保存截图
        screenshot.save(screenshot_path)
        log_message(f"✅ OCR截图已保存: {screenshot_path}")
        
        # 转换为numpy数组
        img_array = np.array(screenshot)
        
        # 转换颜色格式 (RGB -> BGR)
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        
        # 获取OCR实例
        ocr = get_ocr_instance()
        
        # 执行OCR识别
        log_message("正在执行OCR识别...")
        results = ocr.ocr(img_bgr)
        
        # 调试：打印OCR原始结果
        log_message(f"OCR原始结果类型: {type(results)}")
        log_message(f"OCR原始结果长度: {len(results) if results else 'None'}")
        
        # 处理OCR结果
        ocr_texts = []
        if results and len(results) > 0:
            # 获取第一页结果
            page_result = results[0]
            log_message(f"第一页结果类型: {type(page_result)}")

            # 调试：探索OCRResult对象的结构
            if hasattr(page_result, '__dict__'):
                log_message(f"OCRResult对象属性: {list(page_result.__dict__.keys())}")
                for attr_name in page_result.__dict__.keys():
                    attr_value = getattr(page_result, attr_name)
                    log_message(f"  {attr_name}: {type(attr_value)} - {attr_value if len(str(attr_value)) < 100 else str(attr_value)[:100] + '...'}")

            # 尝试不同的访问方式
            try:
                # 方式1：检查是否有rec_texts等属性
                if hasattr(page_result, 'rec_texts') and hasattr(page_result, 'rec_scores'):
                    texts = page_result.rec_texts
                    scores = page_result.rec_scores
                    boxes = getattr(page_result, 'rec_boxes', None)

                    log_message(f"方式1成功：识别到 {len(texts)} 个文本")

                    for i, (text, score) in enumerate(zip(texts, scores)):
                        log_message(f"文本 {i}: '{text}', 置信度: {score:.4f}")

                        if score > 0.3:
                            # 安全地获取边界框
                            bbox = None
                            try:
                                if boxes is not None and i < len(boxes):
                                    bbox = boxes[i]
                            except Exception as e:
                                log_message(f"获取边界框失败: {e}")

                            ocr_texts.append({
                                'text': text,
                                'confidence': score,
                                'bbox': bbox
                            })
                            log_message(f"OCR识别: {text} (置信度: {score:.4f})")
                        else:
                            log_message(f"跳过低置信度文本: '{text}' (置信度: {score:.4f})")

                # 方式2：检查是否是字典格式
                elif isinstance(page_result, dict):
                    if 'rec_texts' in page_result and 'rec_scores' in page_result:
                        texts = page_result['rec_texts']
                        scores = page_result['rec_scores']
                        boxes = page_result.get('rec_boxes', None)

                        log_message(f"方式2成功：识别到 {len(texts)} 个文本")

                        for i, (text, score) in enumerate(zip(texts, scores)):
                            log_message(f"文本 {i}: '{text}', 置信度: {score:.4f}")

                            if score > 0.3:
                                # 安全地获取边界框
                                bbox = None
                                try:
                                    if boxes is not None and i < len(boxes):
                                        bbox = boxes[i]
                                except Exception as e:
                                    log_message(f"获取边界框失败: {e}")

                                ocr_texts.append({
                                    'text': text,
                                    'confidence': score,
                                    'bbox': bbox
                                })
                                log_message(f"OCR识别: {text} (置信度: {score:.4f})")
                            else:
                                log_message(f"跳过低置信度文本: '{text}' (置信度: {score:.4f})")
                    else:
                        log_message(f"字典格式但缺少必要键，可用键: {list(page_result.keys())}")

                # 方式3：尝试标准列表格式
                elif hasattr(page_result, '__iter__'):
                    log_message("尝试标准列表格式...")
                    for i, line in enumerate(page_result):
                        log_message(f"行 {i}: {type(line)} - {line}")
                        if line and len(line) >= 2:
                            box_points = line[0]
                            text_info = line[1]

                            if len(text_info) >= 2:
                                text = text_info[0]
                                confidence = text_info[1]

                                if confidence > 0.3:
                                    ocr_texts.append({
                                        'text': text,
                                        'confidence': confidence,
                                        'bbox': box_points
                                    })
                                    log_message(f"OCR识别: {text} (置信度: {confidence:.4f})")
                                else:
                                    log_message(f"跳过低置信度文本: '{text}' (置信度: {confidence:.4f})")

                else:
                    log_message("无法识别OCR结果格式")

            except Exception as e:
                log_message(f"处理OCR结果时出错: {str(e)}")
                import traceback
                print("错误详细信息:")
                print(traceback.format_exc())
        
        if ocr_texts:
            log_message(f"✅ OCR识别完成，共识别到 {len(ocr_texts)} 个文本")
        else:
            log_message("⚠️ OCR未识别到任何文本")
        
        return ocr_texts, screenshot_path
        
    except Exception as e:
        log_message(f"OCR识别失败: {str(e)}")
        import traceback
        print("错误详细信息:")
        print(traceback.format_exc())
        return [], None

def save_ocr_results(ocr_results, product_index):
    """
    保存OCR识别结果到OCR文件夹
    
    Args:
        ocr_results: OCR识别结果列表
        product_index: 商品编号
    """
    try:
        # 创建OCR文件夹
        ocr_folder = "OCR"
        if not os.path.exists(ocr_folder):
            os.makedirs(ocr_folder)
            log_message(f"创建OCR文件夹: {ocr_folder}")
        
        # 创建OCR结果文件名
        ocr_filename = f"商品{product_index}_OCR结果.txt"
        ocr_filepath = os.path.join(ocr_folder, ocr_filename)
        
        # 保存OCR结果
        with open(ocr_filepath, 'w', encoding='utf-8') as f:
            f.write(f"商品 {product_index} OCR识别结果\n")
            f.write(f"识别时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"识别区域: (265, 282) 到 (691, 883)\n")
            f.write("=" * 50 + "\n\n")
            
            if ocr_results:
                f.write(f"共识别到 {len(ocr_results)} 个文本:\n\n")
                for i, result in enumerate(ocr_results, 1):
                    f.write(f"{i}. {result['text']}\n")
                    f.write(f"   置信度: {result['confidence']:.4f}\n")
                    f.write(f"   边界框: {result['bbox']}\n\n")
            else:
                f.write("未识别到任何文本\n")
        
        log_message(f"✅ OCR结果已保存: {ocr_filepath}")
        return ocr_filepath
        
    except Exception as e:
        log_message(f"保存OCR结果失败: {str(e)}")
        return None

import re
import pyautogui

class SmartOCRAnalyzer:
    """智能OCR分析器 - 与主应用完全一致"""

    def __init__(self, log_callback=None):
        self.log_callback = log_callback or log_message

    def log(self, message):
        """记录日志"""
        self.log_callback(message)

    def extract_size_range(self, texts):
        """提取尺码范围（重新设计版 - 基于位置规则）"""
        import re

        # 有效的服装尺码范围
        valid_sizes = [73, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170]
        sizes = []
        original_size_texts = []

        # 步骤1：找到"参考分类"、"尺码"或"参考身高"的位置（精确匹配）
        size_section_start = -1
        for i, text in enumerate(texts):
            # 精确匹配"参考分类"、"参考身高"、"尺码"，避免匹配到"请选择：颜色分类参考分类"
            if text.strip() == "参考分类" or text.strip() == "参考身高" or "尺码详情" in text or text.strip() == "尺码":
                size_section_start = i
                self.log(f"🎯 找到尺码区域开始位置: 第{i+1}行 '{text}'")
                break

        if size_section_start == -1:
            self.log("⚠️ 未找到尺码区域标识（参考分类/尺码/参考身高）")
            return {
                'optimized_range': '',
                'original_texts': [],
                'size_numbers': []
            }

        # 步骤2：从标识位置开始，提取后续文本中的尺码
        for i in range(size_section_start + 1, len(texts)):
            text = texts[i].strip()
            if not text:
                continue

            # 检查是否到达下一个区域（如果遇到其他商品信息，停止提取）
            stop_keywords = ['颜色分类', '商品', '价格', '一次选多款']
            if any(keyword in text for keyword in stop_keywords):
                self.log(f"🛑 遇到停止关键词，结束尺码提取: '{text}'")
                break

            # 过滤包含￥符号的文本，只保留￥符号前的部分
            if '￥' in text:
                text = text.split('￥')[0].strip()
                self.log(f"🔧 过滤￥符号后的文本: '{text}'")

            # 提取文本中的第一个数字（按您的要求）
            first_number_match = re.search(r'(\d+)', text)
            if first_number_match:
                first_number = int(first_number_match.group(1))

                # 检查是否为有效尺码
                if first_number in valid_sizes:
                    sizes.append(first_number)
                    original_size_texts.append(text)
                    self.log(f"✅ 提取尺码: {first_number} (来源: '{text}')")
                else:
                    self.log(f"⚠️ 跳过无效尺码: {first_number} (来源: '{text}')")
            else:
                # 如果文本不包含数字，可能是尺码区域结束
                self.log(f"📝 尺码区域文本无数字: '{text}'")

        # 去重并排序
        sizes = sorted(list(set(sizes)))

        # 生成优化的尺码范围
        optimized_range = ""
        if sizes:
            if len(sizes) == 1:
                optimized_range = str(sizes[0])
            else:
                optimized_range = f"{sizes[0]}-{sizes[-1]}"

        self.log(f"📊 尺码提取结果: 数字={sizes}, 范围={optimized_range}, 原始文本数量={len(original_size_texts)}")

        return {
            'optimized_range': optimized_range,
            'original_texts': original_size_texts,
            'size_numbers': sizes
        }

    def merge_split_texts(self, ocr_results):
        """智能拼接被分割的文本（增强版 - 支持非相邻拼接）"""
        import re

        self.log("🔧 开始智能拼接被分割的文本...")

        # 第一步：相邻文本拼接
        merged_results = []
        i = 0

        while i < len(ocr_results):
            current = ocr_results[i]
            current_text = current['text']

            # 检查是否需要与下一个文本拼接
            if i + 1 < len(ocr_results):
                next_item = ocr_results[i + 1]
                next_text = next_item['text']

                # 拼接条件判断（基于文本模式，不依赖坐标）
                should_merge = False

                # 条件1：当前文本看起来不完整（以某些字符结尾）
                incomplete_endings = ['短', '长', '（', '(', '+', '、']
                current_incomplete = any(current_text.endswith(ending) for ending in incomplete_endings)

                # 条件2：下一个文本包含价格或以补充字符开头
                next_has_price = bool(re.search(r'[￥¥][\d\.]+', next_text))
                completion_starts = ['）', ')', '袖', '裤', '裙', '衫']
                next_completes = any(next_text.startswith(start) for start in completion_starts)

                # 条件3：特殊模式匹配（针对当前商品的具体问题）
                special_patterns = [
                    # 针对 '两件套（白色短袖' + '+短裤)' 的模式
                    (r'两件套（[^）]*短袖$', r'^\+短裤\)'),
                    # 针对 '两件套（黑色短袖' + '+短裤)' 的模式
                    (r'两件套（[^）]*短袖$', r'^\+短裤\)'),
                    # 原有的模式保留
                    (r'浅粉套装（短袖\+短$', r'^裤）￥[\d\.]+'),
                    (r'.*（.*\+短$', r'^[袖裤裙衫].*￥[\d\.]+'),
                ]

                pattern_match = False
                for current_pattern, next_pattern in special_patterns:
                    if re.search(current_pattern, current_text) and re.search(next_pattern, next_text):
                        pattern_match = True
                        self.log(f"🎯 匹配到特殊模式: '{current_text}' + '{next_text}'")
                        break

                should_merge = (current_incomplete and (next_has_price or next_completes)) or pattern_match

                if should_merge:
                    self.log(f"🔗 检测到分割文本需要拼接:")
                    self.log(f"   当前: '{current_text}'")
                    self.log(f"   下一个: '{next_text}'")
                    self.log(f"   匹配条件: 不完整={current_incomplete}, 有价格={next_has_price}, 补充={next_completes}, 模式={pattern_match}")

                if should_merge:
                    # 拼接文本
                    merged_text = current_text + next_text
                    merged_result = {
                        'text': merged_text,
                        'confidence': min(current['confidence'], next_item['confidence']),
                        'bbox': current.get('bbox', [])  # 使用第一个文本的边界框
                    }
                    merged_results.append(merged_result)
                    self.log(f"✅ 拼接完成: '{merged_text}'")
                    i += 2  # 跳过下一个已拼接的文本
                else:
                    merged_results.append(current)
                    i += 1
            else:
                merged_results.append(current)
                i += 1

        # 第二步：处理非相邻的拼接（针对两件套问题）
        final_results = []
        used_indices = set()

        for i, result in enumerate(merged_results):
            if i in used_indices:
                continue

            text = result['text']

            # 查找需要拼接的两件套文本
            if re.match(r'两件套（[^）]*短袖$', text):
                # 查找对应的 '+短裤)' 文本
                for j in range(i + 1, len(merged_results)):
                    if j in used_indices:
                        continue
                    next_text = merged_results[j]['text']
                    if next_text == '+短裤)':
                        # 找到匹配的文本，进行拼接
                        merged_text = text + next_text
                        merged_result = {
                            'text': merged_text,
                            'confidence': min(result['confidence'], merged_results[j]['confidence']),
                            'bbox': result.get('bbox', [])
                        }
                        final_results.append(merged_result)
                        used_indices.add(j)  # 标记为已使用
                        self.log(f"✅ 非相邻拼接完成: '{text}' + '{next_text}' = '{merged_text}'")
                        break
                else:
                    # 没找到匹配的，保持原样
                    final_results.append(result)
            else:
                final_results.append(result)

        self.log(f"📊 拼接结果: 原始{len(ocr_results)}个文本 → 拼接后{len(final_results)}个文本")

        # 显示拼接后的所有文本（用于调试）
        self.log("📝 拼接后的文本列表:")
        for i, result in enumerate(final_results):
            text = result['text'] if isinstance(result, dict) else result
            self.log(f"   {i+1}. '{text}'")

        return final_results

    def extract_color_classifications(self, ocr_results):
        """提取颜色分类（支持坐标记录版）"""
        color_start_index = -1
        color_end_index = -1

        # 查找"颜色分类"和结束标记的位置
        for i, result in enumerate(ocr_results):
            text = result['text'] if isinstance(result, dict) else result
            if "颜色分类" in text:
                color_start_index = i
                self.log(f"🎯 找到颜色分类开始位置: 第{i}行 '{text}'")
            elif color_start_index != -1 and ("参考分类" in text or "参考身高" in text or "尺码" in text):
                color_end_index = i
                self.log(f"🎯 找到颜色分类结束位置: 第{i}行 '{text}'")
                break

        colors = []
        # 初始化坐标记录字典
        self.color_coordinates = {}

        if color_start_index != -1 and color_end_index != -1:
            self.log(f"🔍 开始提取颜色分类: 从第{color_start_index+1}行到第{color_end_index-1}行")
            # 提取两个标记之间的文本作为颜色分类
            for i in range(color_start_index + 1, color_end_index):
                if i < len(ocr_results):
                    try:
                        result = ocr_results[i]
                        color_text = result['text'].strip() if isinstance(result, dict) else result.strip()
                        bbox = result.get('bbox', []) if isinstance(result, dict) else []

                        self.log(f"🔍 检查颜色文本: '{color_text}' (长度: {len(color_text)})")
                        self.log(f"🔍 bbox类型: {type(bbox)}, bbox内容: {bbox}")

                        if color_text and len(color_text) <= 50:  # 放宽长度限制
                            # 重新设计：根据商品类型分类处理
                            import re

                            # 检查是否包含价格信息（支持多种价格格式）
                            price_patterns = [r'￥[\d\.]+', r'¥[\d\.]+']  # 支持中文和英文货币符号
                            has_price = any(re.search(pattern, color_text) for pattern in price_patterns)

                            self.log(f"🔍 价格检测结果: {has_price}")

                            if has_price:
                                # 第一类：颜色后面直接带价格
                                # 提取纯颜色名称（移除价格）
                                pure_color = re.sub(r'[￥¥][\d\.]+', '', color_text)
                                pure_color = pure_color.strip()

                                # 提取价格（支持多种格式）
                                price_match = re.search(r'[￥¥]([\d\.]+)', color_text)
                                price = price_match.group(1) if price_match else None

                                self.log(f"🔍 价格检测: 文本='{color_text}', 检测到价格={has_price}, 提取价格={price}")

                                if pure_color and len(pure_color) <= 30:
                                    # 记录坐标信息
                                    if bbox is not None and len(bbox) > 0:
                                        coord = self._calculate_click_coordinate(bbox)
                                        if coord:
                                            self.color_coordinates[pure_color] = coord
                                            self.log(f"📍 记录颜色坐标: {pure_color} -> {coord}")

                                    colors.append({
                                        'pure_name': pure_color,
                                        'original_text': color_text,
                                        'has_direct_price': True,
                                        'direct_price': price,
                                        'bbox': bbox
                                    })
                                    self.log(f"✅ 提取到带价格颜色: {pure_color} -> ¥{price} (来源: {color_text})")
                                else:
                                    self.log(f"❌ 跳过带价格颜色: '{pure_color}' (长度: {len(pure_color) if pure_color else 0})")
                            else:
                                # 第二、三、四类：颜色后面不带价格
                                if len(color_text) <= 30:
                                    # 记录坐标信息
                                    if bbox is not None and len(bbox) > 0:
                                        coord = self._calculate_click_coordinate(bbox)
                                        if coord:
                                            self.color_coordinates[color_text] = coord
                                            self.log(f"📍 记录颜色坐标: {color_text} -> {coord}")

                                    colors.append({
                                        'pure_name': color_text,
                                        'original_text': color_text,
                                        'has_direct_price': False,
                                        'direct_price': None,
                                        'bbox': bbox
                                    })
                                    self.log(f"✅ 提取到无价格颜色: {color_text}")
                                else:
                                    self.log(f"❌ 跳过无价格颜色: '{color_text}' (长度: {len(color_text)})")
                        else:
                            self.log(f"❌ 跳过空文本或过长文本: '{color_text}' (长度: {len(color_text) if color_text else 0})")
                    except Exception as e:
                        self.log(f"❌ 处理颜色文本时出错: '{color_text}', 错误: {e}")
                        import traceback
                        self.log(f"错误详情: {traceback.format_exc()}")

        else:
            self.log(f"❌ 未找到颜色分类区域: start_index={color_start_index}, end_index={color_end_index}")

        self.log(f"🎨 颜色分类提取完成: 共提取到 {len(colors)} 个颜色")
        self.log(f"📍 坐标记录完成: 共记录 {len(getattr(self, 'color_coordinates', {}))} 个坐标")

        return colors

    def _calculate_click_coordinate(self, bbox):
        """根据OCR边界框计算点击坐标（中心点）"""
        try:
            # 检查bbox是否为空
            import numpy as np
            if bbox is None:
                return None

            # 转换numpy数组为普通列表
            if isinstance(bbox, np.ndarray):
                bbox = bbox.tolist()

            # 检查长度
            if not bbox or len(bbox) < 4:
                return None

            self.log(f"🔍 处理bbox: {bbox[:8]}... (长度: {len(bbox)})")

            # bbox格式通常是 [x1, y1, x2, y2] 或 [[x1,y1], [x2,y1], [x2,y2], [x1,y2]]
            if isinstance(bbox[0], (list, tuple)):
                # 四个点的格式
                x_coords = [float(point[0]) for point in bbox]
                y_coords = [float(point[1]) for point in bbox]
                center_x = int((min(x_coords) + max(x_coords)) / 2)
                center_y = int((min(y_coords) + max(y_coords)) / 2)
            else:
                # 简单的四个数字格式或扁平化的坐标数组
                if len(bbox) >= 8:
                    # 扁平化的8个数字格式: [x1, y1, x2, y2, x3, y3, x4, y4]
                    x_coords = [float(bbox[i]) for i in range(0, 8, 2)]
                    y_coords = [float(bbox[i]) for i in range(1, 8, 2)]
                    center_x = int((min(x_coords) + max(x_coords)) / 2)
                    center_y = int((min(y_coords) + max(y_coords)) / 2)
                elif len(bbox) >= 4:
                    # 简单的四个数字格式: [x1, y1, x2, y2]
                    center_x = int((float(bbox[0]) + float(bbox[2])) / 2)
                    center_y = int((float(bbox[1]) + float(bbox[3])) / 2)
                else:
                    return None

            self.log(f"✅ 计算坐标成功: ({center_x}, {center_y})")
            # 注意：这里返回的是相对于OCR区域的坐标
            # 在实际点击时需要加上OCR区域的偏移量
            return (center_x, center_y)
        except Exception as e:
            self.log(f"⚠️ 坐标计算失败: {e}, bbox类型: {type(bbox)}")
            return None

    def extract_color_prices(self, texts, color_classifications, analysis_type=None):
        """提取颜色对应的价格（重新设计版）"""
        import re
        color_prices = {}

        # 处理颜色格式
        if color_classifications and isinstance(color_classifications[0], dict):
            # 第一类：多颜色+直接价格
            colors_with_direct_price = [c for c in color_classifications if c.get('has_direct_price', False)]
            if colors_with_direct_price:
                self.log("🎯 第一类商品：从颜色直接提取价格")
                for color_info in colors_with_direct_price:
                    pure_name = color_info['pure_name']
                    direct_price = color_info['direct_price']
                    if direct_price:
                        color_prices[pure_name] = direct_price
                        self.log(f"✅ 直接价格: {pure_name} -> {direct_price}")
                return color_prices

        # 第二类：单颜色+页面价格 或 其他需要从页面提取价格的情况
        self.log("🎯 从页面提取价格信息")

        # 定义价格识别优先级
        price_priority_groups = [
            # 第一优先级组：券前、底价、特卖价
            [
                (r'券前￥(\d+\.?\d*)', '券前价格'),
                (r'底价￥(\d+\.?\d*)', '底价'),
                (r'特卖价￥(\d+\.?\d*)', '特卖价'),
            ],
            # 第二优先级组：券后、特价
            [
                (r'券后￥(\d+\.?\d*)', '券后价格'),
                (r'特价￥(\d+\.?\d*)', '特价'),
            ],
            # 第三优先级组：通用价格
            [
                (r'￥(\d+\.?\d*)', '通用价格'),
            ]
        ]

        # 从页面文本中提取价格
        page_price = None
        for text in texts:
            for priority_group in price_priority_groups:
                found_price = False
                for pattern, pattern_name in priority_group:
                    match = re.search(pattern, text)
                    if match:
                        page_price = match.group(1)
                        self.log(f"✅ 页面{pattern_name}: {page_price} (来源: {text})")
                        found_price = True
                        break
                if found_price:
                    break
            if found_price:
                break

        # 不再自动分配页面价格给颜色，让交互式方案统一处理
        # 这样可以确保所有商品都使用交互式方案获取准确价格

        return color_prices

    def determine_analysis_type(self, color_classifications, color_prices, texts):
        """确定分析类型（重新设计版）"""
        import re

        # 处理颜色格式
        if color_classifications and isinstance(color_classifications[0], dict):
            color_count = len(color_classifications)
            # 检查是否有直接价格的颜色
            colors_with_direct_price = [c for c in color_classifications if c.get('has_direct_price', False)]
            has_direct_color_prices = len(colors_with_direct_price) > 0
        else:
            color_count = len(color_classifications)
            has_direct_color_prices = False

        # 检查页面是否有券前/券后等价格描述
        has_coupon_prices = any(re.search(r'(券前|券后|底价|特卖价|特价)￥', text) for text in texts)

        # 检查尺码是否带价格
        has_size_prices = any(re.search(r'(100|110|120|130|140|150|160|170).*￥', text) for text in texts)

        self.log(f"🔍 商品类型分析:")
        self.log(f"   颜色数量: {color_count}")
        self.log(f"   颜色直接带价格: {has_direct_color_prices}")
        self.log(f"   页面有券前/券后价格: {has_coupon_prices}")
        self.log(f"   尺码带价格: {has_size_prices}")

        # 根据您的四种分类判断
        if color_count > 1 and has_direct_color_prices:
            # 第一类：多颜色+直接价格
            return 'type1_multiple_colors_direct_prices'
        elif color_count <= 1:
            # 第二类：单颜色+页面价格
            return 'type2_single_color_page_prices'
        elif color_count > 1 and not has_direct_color_prices and not has_size_prices:
            # 第三类：多颜色+无价格
            return 'type3_multiple_colors_no_prices'
        elif color_count > 1 and not has_direct_color_prices and has_size_prices:
            # 第四类：多颜色+尺码价格
            return 'type4_multiple_colors_size_prices'
        else:
            return 'unknown'

    def determine_processing_scheme(self, analysis_type):
        """确定处理方案（统一使用互动式方案）"""
        # 所有类型都使用互动式方案，因为它具有最强的兼容性
        return 'interactive'

    def analyze_product_info(self, ocr_results):
        """智能分析商品信息（支持文本拼接版）"""
        try:
            self.log("🧠 开始智能分析商品信息...")

            # 步骤1：智能拼接被分割的文本
            merged_results = self.merge_split_texts(ocr_results)

            # 提取所有文本（用于尺码分析）
            texts = [result['text'] for result in merged_results]

            # 分析尺码范围
            size_info = self.extract_size_range(texts)
            self.log(f"尺码信息: {size_info}")

            # 分析颜色分类（传递完整OCR结果以记录坐标）
            color_classifications = self.extract_color_classifications(merged_results)
            self.log(f"提取到颜色分类: {color_classifications}")
            self.log(f"颜色分类数量: {len(color_classifications)}")
            self.log(f"📍 已记录颜色坐标: {getattr(self, 'color_coordinates', {})}")

            # 分析价格信息
            color_prices = self.extract_color_prices(texts, color_classifications)

            # 确定分析类型和处理方案
            analysis_type = self.determine_analysis_type(color_classifications, color_prices, texts)
            processing_scheme = self.determine_processing_scheme(analysis_type)

            # 标准化颜色分类格式（确保返回纯颜色名称列表）
            if color_classifications and isinstance(color_classifications[0], dict):
                pure_color_names = [color_info['pure_name'] for color_info in color_classifications]
            else:
                pure_color_names = color_classifications

            analysis_result = {
                'size_info': size_info,  # 包含完整尺码信息
                'color_classifications': pure_color_names,  # 返回纯颜色名称
                'color_prices': color_prices,
                'analysis_type': analysis_type,
                'processing_scheme': processing_scheme,
                'color_coordinates': getattr(self, 'color_coordinates', {})  # 添加坐标信息
            }

            self.log(f"📊 分析类型: {analysis_type}")
            self.log(f"🔧 处理方案: {processing_scheme}")

            return analysis_result

        except Exception as e:
            self.log(f"智能分析失败: {e}")
            return {
                'size_info': {'optimized_range': '', 'original_texts': [], 'size_numbers': []},
                'color_classifications': [],
                'color_prices': {},
                'analysis_type': 'unknown',
                'processing_scheme': 'basic'
            }

def update_excel_with_analysis_result(analysis_result, target_link="https://mobile.yangkeduo.com/goods2.html?ps=YyzpL5Jysk"):
    """
    更新Excel文件，将分析结果保存在指定商品链接下方（主应用版本）

    Args:
        analysis_result: 智能分析结果
        target_link: 目标商品链接
    """
    try:
        log_message("💾 更新Excel文件...")

        excel_path = os.path.join("商品图片", "商品SKU信息.xlsx")

        # 读取现有Excel文件（无列名）
        if os.path.exists(excel_path):
            df = pd.read_excel(excel_path, header=None)  # 不使用第一行作为列名
        else:
            log_message("❌ Excel文件不存在，无法更新")
            return False

        # 找到目标商品链接的位置
        target_row = None
        for idx, row in df.iterrows():
            # 检查第二列（索引1）是否包含目标链接
            if len(row) > 1 and pd.notna(row.iloc[1]) and target_link in str(row.iloc[1]):
                target_row = idx
                log_message(f"找到目标商品链接在第 {idx+1} 行")
                break

        if target_row is None:
            log_message(f"❌ 未找到目标商品链接: {target_link}")
            return False

        # 准备要插入的数据（优化格式：颜色和价格在同一行）
        insert_data = []

        # 添加颜色分类和价格信息（颜色和价格在同一行的两个单元格）
        if analysis_result['color_classifications']:
            for color in analysis_result['color_classifications']:
                price = analysis_result['color_prices'].get(color, '未获取')
                # 颜色在第二列，价格在第三列
                insert_data.append([None, color, price])

        # 添加原始尺码信息（数字列表）
        size_info = analysis_result.get('size_info', {})
        if isinstance(size_info, dict):
            size_numbers = size_info.get('size_numbers', [])
            if size_numbers:
                # 保存原始尺码数字列表
                size_list_text = ",".join(map(str, size_numbers))
                insert_data.append([None, size_list_text, None])

            # 添加整理后的尺码范围
            optimized_range = size_info.get('optimized_range', '')
            if optimized_range:
                insert_data.append([None, optimized_range, None])

        # 清空目标链接下方的旧数据，然后插入新数据
        if insert_data:
            # 找到下一个商品链接的位置（用于确定清空范围）
            next_link_row = None
            for idx in range(target_row + 1, len(df)):
                row = df.iloc[idx]
                # 检查是否是另一个商品链接（包含http或https）
                if len(row) > 1 and pd.notna(row.iloc[1]):
                    cell_value = str(row.iloc[1])
                    if 'http' in cell_value and 'yangkeduo.com' in cell_value:
                        next_link_row = idx
                        log_message(f"找到下一个商品链接在第 {idx+1} 行")
                        break

            # 确定清空范围
            if next_link_row is not None:
                # 清空目标链接到下一个链接之间的内容
                before_df = df.iloc[:target_row + 1]
                after_df = df.iloc[next_link_row:]
                log_message(f"清空第 {target_row+2} 行到第 {next_link_row} 行的旧数据")
            else:
                # 清空目标链接到文件末尾的内容
                before_df = df.iloc[:target_row + 1]
                after_df = pd.DataFrame(columns=df.columns)  # 空DataFrame
                log_message(f"清空第 {target_row+2} 行到文件末尾的旧数据")

            # 创建新的DataFrame包含插入的数据（无列名）
            insert_df = pd.DataFrame(insert_data)

            # 合并DataFrame
            new_df = pd.concat([before_df, insert_df, after_df], ignore_index=True)

            # 保存Excel文件（保持原本格式，无列名）
            new_df.to_excel(excel_path, index=False, header=False)
            log_message(f"✅ 分析结果已保存到目标商品下方: {excel_path}")
            log_message(f"   插入了 {len(insert_data)} 行新数据")
            return True
        else:
            log_message("⚠️ 没有数据需要插入")
            return False

    except Exception as e:
        log_message(f"更新Excel失败: {e}")
        import traceback
        log_message(f"错误详情: {traceback.format_exc()}")
        return False

def click_coordinate(x, y, delay=0.5):
    """
    点击指定坐标

    Args:
        x, y: 坐标
        delay: 点击后延迟时间
    """
    try:
        pyautogui.click(x, y)
        time.sleep(delay)
        log_message(f"✅ 点击坐标: ({x}, {y})")
        return True
    except Exception as e:
        log_message(f"❌ 点击坐标失败: {e}")
        return False

def get_middle_size(size_range):
    """
    从尺码范围中获取中间值（避免最小码和最大码）

    Args:
        size_range: 尺码范围字符串，如 "110-170"

    Returns:
        int: 中间尺码值
    """
    try:
        if '-' in size_range:
            min_size, max_size = map(int, size_range.split('-'))

            # 计算中间区域的尺码（避免最小和最大）
            size_list = []
            for size in [73, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170]:
                if min_size <= size <= max_size:
                    size_list.append(size)

            if len(size_list) >= 3:
                # 如果有3个或以上尺码，选择中间的（排除首尾）
                middle_index = len(size_list) // 2
                return size_list[middle_index]
            elif len(size_list) == 2:
                # 如果只有2个尺码，选择较小的那个
                return size_list[0]
            elif len(size_list) == 1:
                # 如果只有1个尺码，就选择它
                return size_list[0]
            else:
                # 如果没有标准尺码，计算数学中间值
                return (min_size + max_size) // 2

        return 130  # 默认值
    except Exception as e:
        log_message(f"获取中间尺码失败: {e}")
        return 130

def find_size_coordinates_from_ocr(middle_size, ocr_region):
    """
    通过OCR识别结果找到尺码的实际坐标

    Args:
        middle_size: 目标尺码
        ocr_region: OCR区域 (x1, y1, x2, y2)

    Returns:
        tuple: (x, y) 坐标，如果未找到返回None
    """
    try:
        log_message(f"🔍 通过OCR查找尺码 {middle_size} 的实际位置...")

        # 截取当前屏幕进行OCR
        x1, y1, x2, y2 = ocr_region
        screenshot = pyautogui.screenshot(region=(x1, y1, x2-x1, y2-y1))

        # 转换为numpy数组
        img_array = np.array(screenshot)
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

        # 获取OCR实例
        ocr = get_ocr_instance()
        results = ocr.ocr(img_bgr)

        if results and len(results) > 0:
            page_result = results[0]
            log_message(f"OCR结果类型: {type(page_result)}")

            # 检查是否是字典格式（参考批量提取秒退拦截的方法）
            if isinstance(page_result, dict) and 'rec_texts' in page_result and 'rec_scores' in page_result and 'rec_boxes' in page_result:
                texts = page_result['rec_texts']
                scores = page_result['rec_scores']
                boxes = page_result['rec_boxes']

                log_message(f"字典格式OCR识别到 {len(texts)} 个文本，查找尺码 {middle_size}")

                for i, (text, score, box) in enumerate(zip(texts, scores, boxes)):
                    log_message(f"文本 {i}: '{text}' (置信度: {score:.3f})")
                    # 修改匹配逻辑：支持包含尺码的文本（如"130￥29.9"）
                    if score > 0.8 and (text.strip() == str(middle_size) or text.startswith(str(middle_size))):
                        # 计算边界框中心点（相对于截图区域）
                        # box格式: [left, top, right, bottom]
                        center_x = int((box[0] + box[2]) / 2)
                        center_y = int((box[1] + box[3]) / 2)

                        # 转换为绝对坐标（相对于屏幕）
                        abs_center_x = center_x + x1
                        abs_center_y = center_y + y1

                        log_message(f"✅ 找到尺码 {middle_size} 在位置: ({abs_center_x}, {abs_center_y}) (来源文本: '{text}')")
                        return (abs_center_x, abs_center_y)

            # 使用属性格式
            elif hasattr(page_result, 'rec_texts') and hasattr(page_result, 'rec_scores') and hasattr(page_result, 'rec_boxes'):
                texts = page_result.rec_texts
                scores = page_result.rec_scores
                boxes = page_result.rec_boxes

                log_message(f"属性格式OCR识别到 {len(texts)} 个文本，查找尺码 {middle_size}")

                for i, (text, score, box) in enumerate(zip(texts, scores, boxes)):
                    log_message(f"文本 {i}: '{text}' (置信度: {score:.3f})")
                    # 修改匹配逻辑：支持包含尺码的文本（如"130￥29.9"）
                    if score > 0.8 and (text.strip() == str(middle_size) or text.startswith(str(middle_size))):
                        # 计算边界框中心点（相对于截图区域）
                        center_x = int((box[0] + box[2]) / 2)
                        center_y = int((box[1] + box[3]) / 2)

                        # 转换为绝对坐标（相对于屏幕）
                        abs_center_x = center_x + x1
                        abs_center_y = center_y + y1

                        log_message(f"✅ 找到尺码 {middle_size} 在位置: ({abs_center_x}, {abs_center_y}) (来源文本: '{text}')")
                        return (abs_center_x, abs_center_y)

            else:
                log_message("OCR结果格式不匹配，尝试备用解析方式...")
                # 打印可用的属性或键
                if isinstance(page_result, dict):
                    log_message(f"字典可用键: {list(page_result.keys())}")
                elif hasattr(page_result, '__dict__'):
                    log_message(f"对象可用属性: {list(page_result.__dict__.keys())}")

                # 备用解析方式（传统格式）
                if hasattr(page_result, '__iter__'):
                    for line in page_result:
                        if line and len(line) >= 2:
                            text_info = line[1]
                            if len(text_info) >= 2:
                                text = text_info[0]
                                confidence = text_info[1]
                                bbox = line[0]

                                log_message(f"备用方式文本: '{text}' (置信度: {confidence:.3f})")
                                if confidence > 0.8 and text.strip() == str(middle_size):
                                    # 计算边界框中心点
                                    center_x = int((bbox[0][0] + bbox[2][0]) / 2)
                                    center_y = int((bbox[0][1] + bbox[2][1]) / 2)

                                    # 转换为绝对坐标
                                    abs_center_x = center_x + x1
                                    abs_center_y = center_y + y1

                                    log_message(f"✅ 备用方式找到尺码 {middle_size} 在位置: ({abs_center_x}, {abs_center_y})")
                                    return (abs_center_x, abs_center_y)

        log_message(f"⚠️ 未通过OCR找到尺码 {middle_size}")
        return None

    except Exception as e:
        log_message(f"通过OCR查找尺码坐标失败: {e}")
        return None

def find_color_coordinates_from_ocr(color_name, ocr_region):
    """
    通过OCR识别结果找到颜色分类的实际坐标

    Args:
        color_name: 颜色分类名称
        ocr_region: OCR区域 (x1, y1, x2, y2)

    Returns:
        tuple: (x, y) 坐标，如果未找到返回None
    """
    try:
        log_message(f"🔍 通过OCR查找颜色 '{color_name}' 的实际位置...")

        # 截取当前屏幕进行OCR
        x1, y1, x2, y2 = ocr_region
        screenshot = pyautogui.screenshot(region=(x1, y1, x2-x1, y2-y1))

        # 转换为numpy数组
        img_array = np.array(screenshot)
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

        # 获取OCR实例
        ocr = get_ocr_instance()
        results = ocr.ocr(img_bgr)

        if results and len(results) > 0:
            page_result = results[0]
            log_message(f"颜色OCR结果类型: {type(page_result)}")

            # 检查是否是字典格式（参考批量提取秒退拦截的方法）
            if isinstance(page_result, dict) and 'rec_texts' in page_result and 'rec_scores' in page_result and 'rec_boxes' in page_result:
                texts = page_result['rec_texts']
                scores = page_result['rec_scores']
                boxes = page_result['rec_boxes']

                log_message(f"字典格式OCR识别到 {len(texts)} 个文本，查找颜色 '{color_name}'")

                for i, (text, score, box) in enumerate(zip(texts, scores, boxes)):
                    log_message(f"文本 {i}: '{text}' (置信度: {score:.3f})")
                    if score > 0.7 and (text.strip() == color_name or color_name in text):
                        # 计算边界框中心点（相对于截图区域）
                        # box格式: [left, top, right, bottom]
                        center_x = int((box[0] + box[2]) / 2)
                        center_y = int((box[1] + box[3]) / 2)

                        # 转换为绝对坐标（相对于屏幕）
                        abs_center_x = center_x + x1
                        abs_center_y = center_y + y1

                        log_message(f"✅ 找到颜色 '{color_name}' 在位置: ({abs_center_x}, {abs_center_y}) (来源文本: '{text}')")
                        return (abs_center_x, abs_center_y)

            # 使用属性格式
            elif hasattr(page_result, 'rec_texts') and hasattr(page_result, 'rec_scores') and hasattr(page_result, 'rec_boxes'):
                texts = page_result.rec_texts
                scores = page_result.rec_scores
                boxes = page_result.rec_boxes

                log_message(f"属性格式OCR识别到 {len(texts)} 个文本，查找颜色 '{color_name}'")

                for i, (text, score, box) in enumerate(zip(texts, scores, boxes)):
                    log_message(f"文本 {i}: '{text}' (置信度: {score:.3f})")
                    if score > 0.7 and (text.strip() == color_name or color_name in text):
                        # 计算边界框中心点（相对于截图区域）
                        center_x = int((box[0] + box[2]) / 2)
                        center_y = int((box[1] + box[3]) / 2)

                        # 转换为绝对坐标（相对于屏幕）
                        abs_center_x = center_x + x1
                        abs_center_y = center_y + y1

                        log_message(f"✅ 找到颜色 '{color_name}' 在位置: ({abs_center_x}, {abs_center_y}) (来源文本: '{text}')")
                        return (abs_center_x, abs_center_y)
            else:
                log_message("OCR结果格式不匹配，尝试备用解析方式...")
                # 备用解析方式
                if hasattr(page_result, '__iter__'):
                    for line in page_result:
                        if line and len(line) >= 2:
                            text_info = line[1]
                            if len(text_info) >= 2:
                                text = text_info[0]
                                confidence = text_info[1]
                                bbox = line[0]

                                log_message(f"备用方式文本: '{text}' (置信度: {confidence:.3f})")
                                if confidence > 0.7 and color_name in text:
                                    # 计算边界框中心点
                                    center_x = int((bbox[0][0] + bbox[2][0]) / 2)
                                    center_y = int((bbox[0][1] + bbox[2][1]) / 2)

                                    # 转换为绝对坐标
                                    abs_center_x = center_x + x1
                                    abs_center_y = center_y + y1

                                    log_message(f"✅ 备用方式找到颜色 '{color_name}' 在位置: ({abs_center_x}, {abs_center_y})")
                                    return (abs_center_x, abs_center_y)

        log_message(f"⚠️ 未通过OCR找到颜色 '{color_name}'")
        return None

    except Exception as e:
        log_message(f"通过OCR查找颜色坐标失败: {e}")
        return None

def find_size_coordinates_by_known_positions(middle_size, ocr_region):
    """
    基于已知实际坐标查找尺码位置
    """
    # 已知的实际坐标（基于您提供的测试数据）
    known_positions = {
        130: (422, 691)
    }

    if middle_size in known_positions:
        coords = known_positions[middle_size]
        log_message(f"✅ 使用已知坐标找到尺码 {middle_size}: {coords}")
        return coords

    # 如果没有已知坐标，尝试根据130的位置估算其他尺码
    if 130 in known_positions:
        base_x, base_y = known_positions[130]
        # 尺码按3列排列: 110 120 130 (第一行)
        #               140 150 160 (第二行)
        #               170         (第三行)

        size_layout = {
            110: (-2, 0),  # 130左边2个位置
            120: (-1, 0),  # 130左边1个位置
            130: (0, 0),   # 基准位置
            140: (-2, 1),  # 下一行左边2个位置
            150: (-1, 1),  # 下一行左边1个位置
            160: (0, 1),   # 下一行同列
            170: (-2, 2)   # 第三行左边2个位置
        }

        if middle_size in size_layout:
            col_offset, row_offset = size_layout[middle_size]
            # 估算每个按钮的间距
            col_spacing = 60  # 列间距
            row_spacing = 45  # 行间距

            estimated_x = base_x + col_offset * col_spacing
            estimated_y = base_y + row_offset * row_spacing

            log_message(f"✅ 基于130位置估算尺码 {middle_size}: ({estimated_x}, {estimated_y})")
            return (estimated_x, estimated_y)

    return None

def find_size_coordinates(middle_size, ocr_region):
    """
    查找尺码坐标（优先使用OCR，备用已知坐标）
    """
    # 首先尝试通过OCR查找
    coords = find_size_coordinates_from_ocr(middle_size, ocr_region)
    if coords:
        return coords

    # 备用：使用已知坐标
    coords = find_size_coordinates_by_known_positions(middle_size, ocr_region)
    if coords:
        return coords

    log_message(f"⚠️ 所有方法都无法找到尺码 {middle_size} 的坐标")
    return None

def find_color_coordinates_by_known_positions(color_name, ocr_region):
    """
    基于已知实际坐标查找颜色位置（仅作为最后备用）
    """
    # 已知的实际坐标（仅用于特定的已测试颜色）
    known_positions = {
        '白色防晒衣': (341, 603),
        '灰色防晒衣': (480, 603),
        '白色': (341, 603),  # 仅用于已测试的特定商品
        '灰色': (480, 603),  # 仅用于已测试的特定商品
    }

    if color_name in known_positions:
        coords = known_positions[color_name]
        log_message(f"⚠️ 使用已知坐标作为最后备用，找到颜色 '{color_name}': {coords}")
        log_message(f"⚠️ 警告：这可能不准确，因为每个商品的颜色布局都不同！")
        return coords

    # 不再进行估算，因为每个商品的颜色分类都不同
    log_message(f"❌ 颜色 '{color_name}' 没有已知坐标，且不能估算（每个商品布局不同）")
    return None

def find_color_coordinates(color_name, ocr_region):
    """
    查找颜色坐标（必须通过OCR精确定位）

    Args:
        color_name: 颜色分类名称
        ocr_region: OCR区域 (x1, y1, x2, y2)

    Returns:
        tuple: (x, y) 坐标，如果OCR无法找到则返回None
    """
    # 必须通过OCR查找，不允许估算
    coords = find_color_coordinates_from_ocr(color_name, ocr_region)
    if coords:
        return coords

    # 如果OCR失败，记录错误并返回None
    log_message(f"❌ 无法通过OCR找到颜色 '{color_name}' 的精确位置")
    log_message(f"❌ 拒绝使用估算坐标，因为每个商品的颜色布局都不同")
    return None

def extract_price_from_screen(ocr_region):
    """
    从屏幕中提取价格信息

    Args:
        ocr_region: OCR区域 (x1, y1, x2, y2)

    Returns:
        str: 提取到的价格，如果未找到返回None
    """
    try:
        log_message("🔍 开始从屏幕提取价格...")

        # 截取当前屏幕进行OCR
        x1, y1, x2, y2 = ocr_region
        screenshot = pyautogui.screenshot(region=(x1, y1, x2-x1, y2-y1))

        # 转换为numpy数组
        img_array = np.array(screenshot)
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

        # 获取OCR实例
        ocr = get_ocr_instance()
        results = ocr.ocr(img_bgr)

        log_message(f"OCR结果类型: {type(results)}, 长度: {len(results) if results else 'None'}")

        if results and len(results) > 0:
            page_result = results[0]

            # 查找价格相关文本 - 扩展模式
            price_patterns = [
                r'底价￥(\d+\.?\d*)',
                r'券前￥(\d+\.?\d*)',
                r'大促底价￥(\d+\.?\d*)',
                r'限\d+件￥(\d+\.?\d*)',
                r'￥(\d+\.?\d*)',
            ]

            all_texts = []

            # 处理OCR结果 - 使用新的API格式
            if hasattr(page_result, 'rec_texts') and hasattr(page_result, 'rec_scores'):
                texts = page_result.rec_texts
                scores = page_result.rec_scores

                log_message(f"识别到 {len(texts)} 个文本")

                for i, (text, score) in enumerate(zip(texts, scores)):
                    all_texts.append(text)
                    log_message(f"文本 {i}: '{text}' (置信度: {score:.3f})")

                    if score > 0.5:  # 较高置信度
                        for pattern in price_patterns:
                            match = re.search(pattern, text)
                            if match:
                                price = match.group(1)
                                log_message(f"✅ 提取到价格: {price} (来源: {text})")
                                return price

                        # 检查是否售罄
                        if "该款式售罄" in text or "售罄" in text:
                            log_message("检测到商品售罄")
                            return "售罄"

            # 备用处理方式
            elif hasattr(page_result, '__iter__'):
                log_message("使用备用OCR结果处理方式")
                for line in page_result:
                    if line and len(line) >= 2:
                        text_info = line[1]
                        if len(text_info) >= 2:
                            text = text_info[0]
                            confidence = text_info[1]
                            all_texts.append(text)

                            log_message(f"备用方式文本: '{text}' (置信度: {confidence:.3f})")

                            if confidence > 0.5:
                                for pattern in price_patterns:
                                    match = re.search(pattern, text)
                                    if match:
                                        price = match.group(1)
                                        log_message(f"✅ 备用方式提取到价格: {price} (来源: {text})")
                                        return price

                                if "该款式售罄" in text or "售罄" in text:
                                    log_message("检测到商品售罄")
                                    return "售罄"

            log_message(f"所有识别文本: {all_texts}")

        log_message("⚠️ 未找到价格信息")
        return None

    except Exception as e:
        log_message(f"从屏幕提取价格失败: {e}")
        import traceback
        log_message(f"错误详情: {traceback.format_exc()}")
        return None

def capture_and_ocr_price_screen(x1, y1, x2, y2, product_index, color_index):
    """
    截取屏幕并进行OCR识别（用于价格提取）

    Args:
        x1, y1, x2, y2: 截图区域坐标
        product_index: 商品编号
        color_index: 颜色索引

    Returns:
        tuple: (ocr_results, screenshot_path)
    """
    try:
        # 截取屏幕
        screenshot = pyautogui.screenshot(region=(x1, y1, x2-x1, y2-y1))

        # 保存截图
        goods_folder = "商品图片"
        if not os.path.exists(goods_folder):
            os.makedirs(goods_folder)

        screenshot_filename = f"{product_index}-{color_index}.jpg"
        screenshot_path = os.path.join(goods_folder, screenshot_filename)
        screenshot.save(screenshot_path)
        log_message(f"✅ 价格OCR截图已保存: {screenshot_path}")

        # 转换为numpy数组进行OCR
        img_array = np.array(screenshot)
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

        # 获取OCR实例并识别
        ocr = get_ocr_instance()
        results = ocr.ocr(img_bgr)

        # 处理OCR结果
        ocr_results = []
        if results and len(results) > 0:
            page_result = results[0]
            log_message(f"价格OCR结果类型: {type(page_result)}")

            # 检查是否是字典格式
            if isinstance(page_result, dict) and 'rec_texts' in page_result and 'rec_scores' in page_result:
                texts = page_result['rec_texts']
                scores = page_result['rec_scores']

                log_message(f"字典格式价格OCR识别到 {len(texts)} 个文本")

                for text, score in zip(texts, scores):
                    ocr_results.append({
                        'text': text,
                        'confidence': score
                    })
                    log_message(f"价格OCR识别: {text} (置信度: {score:.3f})")

            # 使用属性格式
            elif hasattr(page_result, 'rec_texts') and hasattr(page_result, 'rec_scores'):
                texts = page_result.rec_texts
                scores = page_result.rec_scores

                log_message(f"属性格式价格OCR识别到 {len(texts)} 个文本")

                for text, score in zip(texts, scores):
                    ocr_results.append({
                        'text': text,
                        'confidence': score
                    })
                    log_message(f"价格OCR识别: {text} (置信度: {score:.3f})")

            else:
                log_message("价格OCR结果格式不匹配，尝试备用解析...")
                # 打印可用的属性或键
                if isinstance(page_result, dict):
                    log_message(f"字典可用键: {list(page_result.keys())}")
                elif hasattr(page_result, '__dict__'):
                    log_message(f"对象可用属性: {list(page_result.__dict__.keys())}")

                # 备用解析方式（传统格式）
                if hasattr(page_result, '__iter__'):
                    for line in page_result:
                        if line and len(line) >= 2:
                            text_info = line[1]
                            if len(text_info) >= 2:
                                text = text_info[0]
                                confidence = text_info[1]

                                ocr_results.append({
                                    'text': text,
                                    'confidence': confidence
                                })
                                log_message(f"备用方式价格OCR识别: {text} (置信度: {confidence:.3f})")

        log_message(f"✅ 价格OCR识别完成，共识别到 {len(ocr_results)} 个文本")
        return ocr_results, screenshot_path

    except Exception as e:
        log_message(f"价格OCR识别失败: {e}")
        return [], None

def extract_price_from_ocr_results(ocr_results):
    """
    从OCR结果中提取价格（优化版 - 按新的优先级规则）

    Args:
        ocr_results: OCR识别结果列表

    Returns:
        str: 提取到的价格，如果未找到返回None
    """
    try:
        # 按新的优先级规则定义价格模式
        price_priority_groups = [
            # 第一优先级组：券前、底价、特卖价
            [
                (r'券前￥(\d+\.?\d*)', '券前价格'),
                (r'底价￥(\d+\.?\d*)', '底价'),
                (r'特卖价￥(\d+\.?\d*)', '特卖价'),
            ],
            # 第二优先级组：券后、特价
            [
                (r'券后￥(\d+\.?\d*)', '券后价格'),
                (r'特价￥(\d+\.?\d*)', '特价'),
            ],
            # 第三优先级组：通用价格
            [
                (r'￥(\d+\.?\d*)', '通用价格'),
            ]
        ]

        # 首先检查是否售罄
        for result in ocr_results:
            text = result['text']
            confidence = result['confidence']

            if confidence > 0.5 and ("该款式售罄" in text or "售罄" in text):
                log_message("检测到商品售罄")
                return "售罄"

        # 按优先级组查找价格
        for group_index, priority_group in enumerate(price_priority_groups, 1):
            log_message(f"🔍 搜索第{group_index}优先级组价格...")

            for result in ocr_results:
                text = result['text']
                confidence = result['confidence']

                if confidence > 0.5:  # 较高置信度
                    for pattern, pattern_name in priority_group:
                        match = re.search(pattern, text)
                        if match:
                            price = match.group(1)
                            log_message(f"✅ 提取到{pattern_name}: {price} (来源: {text})")
                            return price

            # 如果在当前优先级组找到价格，就不再搜索下一组
            log_message(f"第{group_index}优先级组未找到价格，继续搜索下一组...")

        log_message("⚠️ 未找到任何价格信息")
        return None

    except Exception as e:
        log_message(f"从OCR结果提取价格失败: {e}")
        return None

def interactive_price_extraction(analysis_result, ocr_screenshot_region, debug_mode=False, product_index=1):
    """
    交互式价格提取（重写版本）

    Args:
        analysis_result: 分析结果
        ocr_screenshot_region: OCR截图区域 (x1, y1, x2, y2)
        debug_mode: 调试模式，只显示坐标不实际点击
        product_index: 商品编号

    Returns:
        dict: 更新后的颜色价格字典
    """
    try:
        if analysis_result['analysis_type'] not in ['multiple_colors_no_prices', 'multiple_colors_partial_prices']:
            log_message("无需交互式价格提取")
            return analysis_result['color_prices']

        log_message("🚀 开始交互式价格提取...")

        # 获取需要交互获取价格的颜色
        colors_need_interaction = []
        for color in analysis_result['color_classifications']:
            if color not in analysis_result['color_prices']:
                colors_need_interaction.append(color)

        if not colors_need_interaction:
            log_message("所有颜色都已有价格，无需交互")
            return analysis_result['color_prices']

        log_message(f"需要交互获取价格的颜色: {colors_need_interaction}")

        # 获取中间尺码
        middle_size = get_middle_size(analysis_result['size_range'])
        log_message(f"选择中间尺码: {middle_size}")

        # 步骤1：点击中间尺码
        if not debug_mode:
            size_coords = find_size_coordinates(middle_size, ocr_screenshot_region)
            if size_coords:
                log_message(f"🎯 点击尺码 {middle_size} 坐标: {size_coords}")
                click_coordinate(size_coords[0], size_coords[1], 2.0)  # 等待2秒让页面更新
            else:
                log_message(f"⚠️ 未找到尺码 {middle_size} 的坐标，跳过尺码点击")
        else:
            log_message(f"🔍 调试模式 - 模拟点击尺码 {middle_size}")

        # 复制原始价格字典
        updated_prices = analysis_result['color_prices'].copy()
        x1, y1, x2, y2 = ocr_screenshot_region

        # 步骤2：依次点击每个颜色并获取价格
        for i, color in enumerate(colors_need_interaction):
            log_message(f"🎨 处理颜色 {i+1}/{len(colors_need_interaction)}: {color}")

            if not debug_mode:
                # 点击颜色
                color_coords = find_color_coordinates(color, ocr_screenshot_region)
                if color_coords:
                    log_message(f"🎯 点击颜色 {color} 坐标: {color_coords}")
                    click_coordinate(color_coords[0], color_coords[1], 2.0)  # 等待2秒让页面更新

                    # 步骤3：截图并OCR识别更新后的页面
                    log_message(f"📸 截取颜色 {color} 更新后的页面...")
                    price_ocr_results, screenshot_path = capture_and_ocr_price_screen(x1, y1, x2, y2, product_index, i+1)

                    # 步骤4：从新的OCR结果中提取价格
                    if price_ocr_results:
                        price = extract_price_from_ocr_results(price_ocr_results)
                        if price:
                            if price == "售罄":
                                log_message(f"颜色 {color} 已售罄，跳过")
                            else:
                                updated_prices[color] = price
                                log_message(f"✅ 获取到颜色 {color} 的价格: {price}")
                        else:
                            log_message(f"⚠️ 未能从OCR结果中提取颜色 {color} 的价格")
                            updated_prices[color] = "获取失败"
                    else:
                        log_message(f"⚠️ 颜色 {color} 的OCR识别失败")
                        updated_prices[color] = "OCR失败"
                else:
                    log_message(f"⚠️ 未找到颜色 {color} 的坐标")
                    updated_prices[color] = "坐标未找到"
            else:
                # 调试模式下，模拟获取价格
                log_message(f"🔍 调试模式 - 模拟处理颜色 {color}")
                updated_prices[color] = "调试模式-未实际获取"

        log_message("🎉 交互式价格提取完成")
        return updated_prices

    except Exception as e:
        log_message(f"交互式价格提取失败: {e}")
        import traceback
        log_message(f"错误详情: {traceback.format_exc()}")
        return analysis_result['color_prices']

def extract_size_range(ocr_texts):
    """
    提取尺码范围

    Args:
        ocr_texts: OCR识别结果列表

    Returns:
        str: 尺码范围，如 "100-170"
    """
    try:
        # 定义有效的服装尺码列表
        valid_sizes = [73, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170]
        sizes = []

        for result in ocr_texts:
            text = result['text']

            # 查找纯数字（尺码）
            if text.isdigit():
                size_num = int(text)
                if size_num in valid_sizes:
                    sizes.append(size_num)
                    log_message(f"✅ 识别到有效尺码: {size_num} (来源: {text})")
                else:
                    log_message(f"⚠️ 过滤无效尺码: {size_num} (来源: {text})")
            else:
                # 从包含价格的文本中提取尺码（如 "110￥25.88"）
                import re
                size_match = re.match(r'^(\d+)￥', text)
                if size_match:
                    size_num = int(size_match.group(1))
                    if size_num in valid_sizes:
                        sizes.append(size_num)
                        log_message(f"✅ 从价格文本识别到有效尺码: {size_num} (来源: {text})")
                    else:
                        log_message(f"⚠️ 从价格文本过滤无效尺码: {size_num} (来源: {text})")

        if sizes:
            sizes = sorted(set(sizes))  # 去重并排序
            min_size = min(sizes)
            max_size = max(sizes)
            return f"{min_size}-{max_size}"

        return "未识别到尺码"

    except Exception as e:
        log_message(f"提取尺码范围失败: {e}")
        return "提取失败"

def extract_color_classifications(ocr_texts):
    """
    提取颜色分类名称（基于文本顺序和内容分析）

    Args:
        ocr_texts: OCR识别结果列表

    Returns:
        list: 颜色分类名称列表
    """
    try:
        # 找到"颜色分类"和"参考分类"的索引位置
        color_index = None
        ref_index = None

        for i, result in enumerate(ocr_texts):
            text = result['text']

            if "颜色分类" == text:
                color_index = i
                log_message(f"找到颜色分类位置: 索引{i}")

            if "参考分类" == text or "参考身高" == text:
                ref_index = i
                log_message(f"找到参考分类/参考身高位置: 索引{i}")

        if color_index is None or ref_index is None:
            log_message("未找到颜色分类或参考分类/参考身高标识")
            return []

        # 提取在颜色分类和参考分类之间的文本
        color_texts = []
        for i in range(color_index + 1, ref_index):
            if i < len(ocr_texts):
                text = ocr_texts[i]['text']

                # 跳过一些明显不是颜色分类的文本
                skip_patterns = [
                    r'^\d+$',  # 纯数字
                    r'^[+\-]$',  # 加减号
                    r'尺码详情',  # 尺码相关
                    r'请选择',  # 选择提示
                    r'一次选多款',  # 操作提示
                    r'快要抢光',  # 库存提示信息
                ]

                should_skip = False
                for pattern in skip_patterns:
                    if re.match(pattern, text):
                        should_skip = True
                        break

                if not should_skip and text.strip():
                    color_texts.append(text)

        # 处理文本拼接和分割
        processed_colors = []

        for text in color_texts:
            # 如果包含价格符号，提取颜色部分
            if '￥' in text:
                color_part = text.split('￥')[0]
                if color_part.strip():
                    processed_colors.append(color_part.strip())
            else:
                processed_colors.append(text)

        # 智能拼接和分割
        final_colors = []
        used_indices = set()  # 记录已使用的索引

        # 第一遍：处理需要拼接的文本
        for i in range(len(processed_colors)):
            if i in used_indices:
                continue

            current_text = processed_colors[i]
            if current_text is None:
                continue

            # 特殊处理：查找需要拼接的文本片段
            if current_text.endswith(('（', '(', '+', '短')):
                # 寻找匹配的结尾部分
                merged_text = current_text
                used_indices.add(i)

                # 查找匹配的结尾
                for j in range(i + 1, len(processed_colors)):
                    next_text = processed_colors[j]
                    if next_text is None:
                        continue

                    # 如果找到以）结尾的文本，这可能是我们要的结尾
                    if next_text.startswith(('）', ')', '裤')):
                        merged_text += next_text
                        used_indices.add(j)
                        break

                final_colors.append(merged_text)

        # 第二遍：处理剩余的独立文本
        for i in range(len(processed_colors)):
            if i in used_indices:
                continue

            current_text = processed_colors[i]
            if current_text is None:
                continue

            # 检查是否需要分割（多个颜色在一个文本中）
            split_patterns = [
                r'(白色\w+)(灰色\w+)',  # 白色xxx灰色xxx
                r'(浅粉\w+)(灰色\w+)',  # 浅粉xxx灰色xxx
                r'(白色)(蓝色)',  # 白色蓝色
                r'(白色)(灰色)',  # 白色灰色
                r'(\w+色)(\w+色)',  # 通用：xxx色xxx色
            ]

            split_found = False
            for pattern in split_patterns:
                match = re.match(pattern, current_text)
                if match:
                    # 分割成两个颜色
                    color1 = match.group(1)
                    color2 = match.group(2)
                    final_colors.append(color1)
                    final_colors.append(color2)
                    split_found = True
                    break

            if not split_found:
                # 不需要分割，直接添加
                final_colors.append(current_text)

        log_message(f"提取到颜色分类: {final_colors}")
        return final_colors

    except Exception as e:
        log_message(f"提取颜色分类失败: {e}")
        return []

def extract_prices_from_colors(ocr_texts, color_classifications):
    """
    从颜色分类文本中提取价格

    Args:
        ocr_texts: OCR识别结果列表
        color_classifications: 颜色分类列表

    Returns:
        dict: {颜色分类: 价格} 的字典
    """
    try:
        color_prices = {}

        # 收集所有包含价格的文本
        price_texts = []
        for result in ocr_texts:
            text = result['text']
            if '￥' in text:
                price_texts.append(text)

        # 智能匹配颜色和价格
        for color in color_classifications:
            found_price = None

            # 方法1：直接在颜色分类文本中查找价格
            for price_text in price_texts:
                if color in price_text:
                    price_match = re.search(r'￥(\d+\.?\d*)', price_text)
                    if price_match:
                        found_price = price_match.group(1)
                        log_message(f"直接匹配找到颜色价格: {color} -> {found_price}")
                        break

            # 方法2：特殊处理拼接的颜色分类（如"浅粉套装（短袖+短裤）"）
            if not found_price and '（' in color and '）' in color:
                # 查找以"裤）"开头的价格文本
                for price_text in price_texts:
                    if price_text.startswith('裤）'):
                        price_match = re.search(r'￥(\d+\.?\d*)', price_text)
                        if price_match:
                            found_price = price_match.group(1)
                            log_message(f"拼接匹配找到颜色价格: {color} -> {found_price}")
                            break

            # 方法3：如果没有直接匹配，尝试模糊匹配
            if not found_price:
                # 提取颜色的关键词
                color_keywords = []
                if '浅粉' in color:
                    color_keywords.extend(['浅粉', '粉'])
                if '灰色' in color:
                    color_keywords.extend(['灰色', '灰'])
                if '短袖' in color:
                    color_keywords.extend(['短袖'])
                if '短裤' in color or '裤' in color:
                    color_keywords.extend(['裤', '短裤'])
                if '套装' in color:
                    color_keywords.extend(['套装'])

                # 在价格文本中查找关键词
                for price_text in price_texts:
                    for keyword in color_keywords:
                        if keyword in price_text:
                            price_match = re.search(r'￥(\d+\.?\d*)', price_text)
                            if price_match:
                                found_price = price_match.group(1)
                                log_message(f"关键词匹配找到颜色价格: {color} (关键词:{keyword}) -> {found_price}")
                                break
                    if found_price:
                        break

            if found_price:
                color_prices[color] = found_price

        return color_prices

    except Exception as e:
        log_message(f"提取颜色价格失败: {e}")
        return {}

def extract_single_price(ocr_texts):
    """
    提取单一价格（适用于只有一个价格的商品）

    Args:
        ocr_texts: OCR识别结果列表

    Returns:
        str: 价格，如果找到的话
    """
    try:
        prices = []
        for result in ocr_texts:
            text = result['text']

            # 查找￥价格
            price_matches = re.findall(r'￥(\d+\.?\d*)', text)
            for price in price_matches:
                prices.append(price)

        if len(prices) == 1:
            log_message(f"找到单一价格: {prices[0]}")
            return prices[0]
        elif len(prices) > 1:
            log_message(f"找到多个价格: {prices}")
            # 返回第一个价格作为默认
            return prices[0]

        return None

    except Exception as e:
        log_message(f"提取单一价格失败: {e}")
        return None

def analyze_product_info(ocr_texts):
    """
    智能分析商品信息

    Args:
        ocr_texts: OCR识别结果列表

    Returns:
        dict: 包含颜色分类、价格、尺码的分析结果
    """
    try:
        log_message("开始智能分析商品信息...")

        # 1. 提取尺码范围
        size_range = extract_size_range(ocr_texts)
        log_message(f"尺码范围: {size_range}")

        # 2. 提取颜色分类
        color_classifications = extract_color_classifications(ocr_texts)
        log_message(f"颜色分类数量: {len(color_classifications)}")

        # 3. 分析价格策略
        analysis_result = {
            'size_range': size_range,
            'color_classifications': color_classifications,
            'color_prices': {},
            'analysis_type': 'unknown'
        }

        if len(color_classifications) == 0:
            log_message("未找到颜色分类")
            analysis_result['analysis_type'] = 'no_colors'

        elif len(color_classifications) == 1:
            # 单一颜色分类，查找统一价格
            single_price = extract_single_price(ocr_texts)
            if single_price:
                analysis_result['color_prices'][color_classifications[0]] = single_price
                analysis_result['analysis_type'] = 'single_color_single_price'
                log_message(f"单一颜色单一价格: {color_classifications[0]} -> {single_price}")
            else:
                analysis_result['analysis_type'] = 'single_color_no_price'
                log_message("单一颜色但未找到价格")

        else:
            # 多个颜色分类，检查是否有直接价格
            color_prices = extract_prices_from_colors(ocr_texts, color_classifications)
            analysis_result['color_prices'] = color_prices

            if len(color_prices) == len(color_classifications):
                analysis_result['analysis_type'] = 'multiple_colors_with_prices'
                log_message("多颜色且都有价格")
            elif len(color_prices) > 0:
                analysis_result['analysis_type'] = 'multiple_colors_partial_prices'
                log_message("多颜色但只有部分价格")
            else:
                analysis_result['analysis_type'] = 'multiple_colors_no_prices'
                log_message("多颜色但无直接价格，需要交互获取")

        return analysis_result

    except Exception as e:
        log_message(f"智能分析失败: {e}")
        return {
            'size_range': '分析失败',
            'color_classifications': [],
            'color_prices': {},
            'analysis_type': 'error'
        }

def create_or_update_sku_excel(ocr_results, product_index, enable_interaction=False, ocr_region=None, debug_mode=False):
    """
    创建或更新商品SKU信息Excel文件（智能版本）

    Args:
        ocr_results: OCR识别结果列表
        product_index: 商品编号
        enable_interaction: 是否启用交互式价格获取
        ocr_region: OCR截图区域 (x1, y1, x2, y2)
        debug_mode: 调试模式，只显示坐标不实际点击
    """
    try:
        # Excel文件路径
        goods_folder = "商品图片"
        excel_filename = "商品SKU信息.xlsx"
        excel_filepath = os.path.join(goods_folder, excel_filename)

        # 智能分析商品信息
        if ocr_results:
            analysis = analyze_product_info(ocr_results)

            # 如果启用交互式价格获取，尝试获取缺失的价格
            if enable_interaction and ocr_region:
                analysis['color_prices'] = interactive_price_extraction(analysis, ocr_region, debug_mode, product_index)
        else:
            analysis = {
                'size_range': '未识别到文本',
                'color_classifications': [],
                'color_prices': {},
                'analysis_type': 'no_ocr'
            }

        # 准备数据行
        data_rows = []

        # 添加尺码信息行
        data_rows.append({
            '商品编号': product_index,
            '颜色分类': analysis['size_range'],
            '价格': ''
        })

        # 添加颜色分类和价格行
        if analysis['color_classifications']:
            for color in analysis['color_classifications']:
                price = analysis['color_prices'].get(color, '需要交互获取')
                data_rows.append({
                    '商品编号': '',  # 只在第一行显示商品编号
                    '颜色分类': color,
                    '价格': price
                })
        else:
            # 如果没有颜色分类，添加一个提示行
            data_rows.append({
                '商品编号': '',
                '颜色分类': '未识别到颜色分类',
                '价格': ''
            })

        # 检查Excel文件是否存在
        if os.path.exists(excel_filepath):
            # 读取现有数据
            df = pd.read_excel(excel_filepath)
            log_message(f"读取现有Excel文件，当前有 {len(df)} 行数据")
        else:
            # 创建新的DataFrame
            df = pd.DataFrame(columns=['商品编号', '颜色分类', '价格'])
            log_message("创建新的Excel文件")

        # 添加空行分隔
        if not df.empty:
            empty_row = pd.DataFrame([{'商品编号': '', '颜色分类': '', '价格': ''}])
            df = pd.concat([df, empty_row], ignore_index=True)

        # 添加新的商品数据
        new_rows = pd.DataFrame(data_rows)
        df = pd.concat([df, new_rows], ignore_index=True)

        # 保存Excel文件
        df.to_excel(excel_filepath, index=False)
        log_message(f"✅ 商品SKU信息已保存到Excel: {excel_filepath}")

        # 打印分析结果摘要
        log_message("=" * 40)
        log_message("智能分析结果摘要:")
        log_message(f"  尺码范围: {analysis['size_range']}")
        log_message(f"  颜色分类数量: {len(analysis['color_classifications'])}")
        log_message(f"  分析类型: {analysis['analysis_type']}")
        if analysis['color_prices']:
            log_message("  颜色价格:")
            for color, price in analysis['color_prices'].items():
                log_message(f"    {color}: {price}")
        log_message("=" * 40)

        return excel_filepath

    except Exception as e:
        log_message(f"保存商品SKU信息失败: {str(e)}")
        return None

def parse_ocr_file(filepath):
    """
    解析OCR结果文件

    Args:
        filepath: OCR结果文件路径

    Returns:
        list: OCR识别结果列表
    """
    try:
        ocr_results = []

        with open(filepath, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        current_text = None
        current_confidence = None
        current_bbox = None

        for line in lines:
            line = line.strip()

            # 跳过空行和分隔线
            if not line or line.startswith('=') or '识别时间:' in line or '识别区域:' in line or '共识别到' in line:
                continue

            # 检查是否是文本行（以数字开头）
            if re.match(r'^\d+\.\s+(.+)', line):
                # 保存上一个结果
                if current_text and current_confidence is not None:
                    ocr_results.append({
                        'text': current_text,
                        'confidence': current_confidence,
                        'bbox': current_bbox
                    })

                # 提取新的文本
                match = re.match(r'^\d+\.\s+(.+)', line)
                current_text = match.group(1)
                current_confidence = None
                current_bbox = None

            elif line.startswith('置信度:'):
                # 提取置信度
                confidence_match = re.search(r'置信度:\s*(\d+\.?\d*)', line)
                if confidence_match:
                    current_confidence = float(confidence_match.group(1))

            elif line.startswith('边界框:'):
                # 提取边界框（简化处理）
                current_bbox = line.replace('边界框:', '').strip()

        # 保存最后一个结果
        if current_text and current_confidence is not None:
            ocr_results.append({
                'text': current_text,
                'confidence': current_confidence,
                'bbox': current_bbox
            })

        log_message(f"从文件解析到 {len(ocr_results)} 个OCR结果")
        return ocr_results

    except Exception as e:
        log_message(f"解析OCR文件失败: {e}")
        return []

def test_existing_ocr_files():
    """测试现有的OCR文件"""
    log_message("开始测试现有OCR文件...")

    ocr_folder = "OCR"
    if not os.path.exists(ocr_folder):
        log_message("OCR文件夹不存在")
        return

    # 获取所有OCR文件
    ocr_files = [f for f in os.listdir(ocr_folder) if f.endswith('_OCR结果.txt')]

    if not ocr_files:
        log_message("未找到OCR结果文件")
        return

    log_message(f"找到 {len(ocr_files)} 个OCR文件")

    for ocr_file in sorted(ocr_files):
        log_message(f"\n处理文件: {ocr_file}")

        # 提取商品编号
        match = re.search(r'商品(\d+)_OCR结果\.txt', ocr_file)
        if match:
            product_index = match.group(1)
        else:
            product_index = "未知"

        # 解析OCR文件
        filepath = os.path.join(ocr_folder, ocr_file)
        ocr_results = parse_ocr_file(filepath)

        if ocr_results:
            log_message(f"商品{product_index}的OCR结果:")
            for i, result in enumerate(ocr_results, 1):
                log_message(f"  {i}. {result['text']} (置信度: {result['confidence']:.2f})")

            # 创建或更新商品SKU信息Excel文件
            excel_path = create_or_update_sku_excel(ocr_results, product_index)

            log_message(f"✅ 商品{product_index}处理完成")
        else:
            log_message(f"❌ 商品{product_index}无有效OCR结果")

def main():
    """主函数 - 完整版智能OCR测试"""
    print("=" * 60)
    print("OCR快速测试应用 - 完整版")
    print("集成主应用的完整OCR识别方案")
    print("=" * 60)

    log_message("🚀 智能OCR测试即将开始...")
    log_message("📋 功能说明：")
    log_message("  - 5秒后自动开始第一次OCR识别")
    log_message("  - 智能分析商品类型并自动选择处理方案")
    log_message("  - 全自动执行交互式价格获取（无需用户干预）")
    log_message("  - 自动更新Excel文件中的指定商品链接")
    log_message("  - 与主应用功能完全一致")

    log_message("⚠️ 请确保：")
    log_message("  - 目标区域(265, 282)到(691, 883)可见")
    log_message("  - 商品图片/商品SKU信息.xlsx 文件存在")
    log_message("  - 目标商品链接: https://mobile.yangkeduo.com/goods2.html?ps=YyzpL5Jysk")

    # 5秒倒计时
    for i in range(5, 0, -1):
        log_message(f"⏰ 倒计时: {i} 秒")
        time.sleep(1)

    log_message("🎯 开始智能OCR分析...")

    # 定义OCR识别区域（与主应用一致）
    x1, y1 = 270, 230
    x2, y2 = 691, 883
    ocr_region = (x1, y1, x2, y2)
    product_index = 1

    # 步骤1：执行OCR识别并保存截图
    log_message("📸 步骤1：执行OCR识别...")
    ocr_results, screenshot_path = capture_and_ocr_screen(x1, y1, x2, y2, product_index)

    if not ocr_results:
        log_message("❌ OCR识别失败，无法继续")
        return

    log_message(f"✅ OCR识别完成，共识别到 {len(ocr_results)} 个文本")

    # 步骤2：智能分析商品信息
    log_message("🧠 步骤2：智能分析商品信息...")
    analyzer = SmartOCRAnalyzer()
    analysis_result = analyzer.analyze_product_info(ocr_results)

    # 显示分析结果
    log_message("📊 智能分析结果:")
    size_info = analysis_result.get('size_info', {})
    if isinstance(size_info, dict):
        log_message(f"  优化尺码范围: {size_info.get('optimized_range', '')}")
        log_message(f"  原始尺码文本: {size_info.get('original_texts', [])}")
    log_message(f"  颜色分类: {analysis_result['color_classifications']}")
    log_message(f"  颜色价格: {analysis_result['color_prices']}")
    log_message(f"  分析类型: {analysis_result['analysis_type']}")
    log_message(f"  处理方案: {analysis_result['processing_scheme']}")

    # 步骤3：根据处理方案执行相应操作
    log_message("🔧 步骤3：执行处理方案...")

    # 统一使用交互式价格获取方案（具有最强兼容性）
    log_message("🔄 系统自动执行交互式价格获取方案")
    log_message("🎯 开始实际点击操作获取价格...")

    # 直接执行交互式价格获取（不使用调试模式）
    updated_prices = execute_interactive_price_extraction(analysis_result, ocr_region, debug_mode=False, product_index=product_index)
    analysis_result['color_prices'] = updated_prices

    # 步骤4：更新Excel文件
    log_message("💾 步骤4：更新Excel文件...")
    success = update_excel_with_analysis_result(analysis_result)

    if success:
        log_message("✅ Excel文件更新成功")
    else:
        log_message("❌ Excel文件更新失败")

    # 步骤5：保存OCR结果到文件
    log_message("📄 步骤5：保存OCR结果...")
    save_ocr_results(ocr_results, product_index)

    log_message("=" * 60)
    log_message("🎉 智能OCR测试完成！")
    log_message("📋 处理结果总结：")
    log_message(f"  - 识别文本数量: {len(ocr_results)}")
    log_message(f"  - 颜色分类数量: {len(analysis_result['color_classifications'])}")
    log_message(f"  - 获取价格数量: {len(analysis_result['color_prices'])}")
    log_message(f"  - 处理方案: {analysis_result['processing_scheme']}")
    log_message("=" * 60)

def execute_interactive_price_extraction(analysis_result, ocr_region, debug_mode=False, product_index=1):
    """
    执行交互式价格获取（主应用版本 - 全自动）

    Args:
        analysis_result: 分析结果
        ocr_region: OCR区域 (x1, y1, x2, y2)
        debug_mode: 调试模式（已废弃，保持兼容性）
        product_index: 商品编号

    Returns:
        dict: 更新后的颜色价格字典
    """
    try:
        log_message("🚀 开始交互式价格获取...")

        # 获取需要交互获取价格的颜色
        colors_need_interaction = []
        for color in analysis_result['color_classifications']:
            if color not in analysis_result['color_prices']:
                colors_need_interaction.append(color)

        if not colors_need_interaction:
            log_message("所有颜色都已有价格，无需交互")
            return analysis_result['color_prices']

        log_message(f"需要交互获取价格的颜色: {colors_need_interaction}")

        # 获取中间尺码
        size_info = analysis_result.get('size_info', {})
        middle_size = get_middle_size_from_info(size_info)
        log_message(f"选择中间尺码: {middle_size}")

        # 步骤1：点击中间尺码
        size_coords = find_size_coordinates(middle_size, ocr_region)
        if size_coords:
            log_message(f"🎯 点击尺码 {middle_size} 坐标: {size_coords}")
            click_coordinate(size_coords[0], size_coords[1], 2.0)
        else:
            log_message(f"⚠️ 未找到尺码 {middle_size} 的坐标")

        # 复制原始价格字典
        updated_prices = analysis_result['color_prices'].copy()
        x1, y1, x2, y2 = ocr_region

        # 步骤2：依次点击每个颜色并获取价格
        for i, color in enumerate(colors_need_interaction):
            log_message(f"🎨 处理颜色 {i+1}/{len(colors_need_interaction)}: {color}")

            # 优先使用记录的坐标，如果没有则使用OCR查找
            color_coords = None

            # 尝试从分析结果中获取记录的坐标
            color_coordinates = analysis_result.get('color_coordinates', {})
            if color in color_coordinates:
                # 获取相对坐标并转换为绝对坐标
                relative_coords = color_coordinates[color]
                absolute_x = x1 + relative_coords[0]
                absolute_y = y1 + relative_coords[1]
                color_coords = (absolute_x, absolute_y)
                log_message(f"📍 使用记录的坐标: {color}")
                log_message(f"   相对坐标: {relative_coords}")
                log_message(f"   绝对坐标: {color_coords}")
            else:
                # 回退到OCR查找坐标
                color_coords = find_color_coordinates(color, ocr_region)
                log_message(f"🔍 OCR查找坐标: {color} -> {color_coords}")

            if color_coords:
                log_message(f"🎯 点击颜色 {color} 坐标: {color_coords}")
                click_coordinate(color_coords[0], color_coords[1], 2.0)

                # 截图并OCR识别更新后的页面
                log_message(f"📸 截取颜色 {color} 更新后的页面...")
                price_ocr_results, screenshot_path = capture_and_ocr_price_screen(x1, y1, x2, y2, product_index, i+1)

                # 从新的OCR结果中提取价格
                if price_ocr_results:
                    price = extract_price_from_ocr_results(price_ocr_results)
                    if price:
                        if price == "售罄":
                            log_message(f"颜色 {color} 已售罄，跳过")
                        else:
                            updated_prices[color] = price
                            log_message(f"✅ 获取到颜色 {color} 的价格: {price}")
                    else:
                        log_message(f"⚠️ 未能从OCR结果中提取颜色 {color} 的价格")
                        updated_prices[color] = "获取失败"
                else:
                    log_message(f"⚠️ 颜色 {color} 的OCR识别失败")
                    updated_prices[color] = "OCR失败"
            else:
                log_message(f"⚠️ 未找到颜色 {color} 的坐标")
                updated_prices[color] = "坐标未找到"

        log_message("🎉 交互式价格获取完成")
        return updated_prices

    except Exception as e:
        log_message(f"交互式价格获取失败: {e}")
        import traceback
        log_message(f"错误详情: {traceback.format_exc()}")
        return analysis_result['color_prices']

def get_middle_size_from_info(size_info):
    """从尺码信息中获取中间尺码（避免最小码和最大码）"""
    if isinstance(size_info, dict):
        size_numbers = size_info.get('size_numbers', [])
        if size_numbers:
            # 排序尺码列表
            sorted_sizes = sorted(size_numbers)

            if len(sorted_sizes) >= 3:
                # 如果有3个或以上尺码，选择中间的（排除首尾）
                middle_index = len(sorted_sizes) // 2
                selected_size = sorted_sizes[middle_index]
                log_message(f"📏 尺码选择策略: 从{len(sorted_sizes)}个尺码中选择中间值 {selected_size}")
                log_message(f"   完整尺码列表: {sorted_sizes}")
                log_message(f"   避免最小码: {sorted_sizes[0]}，避免最大码: {sorted_sizes[-1]}")
                return selected_size
            elif len(sorted_sizes) == 2:
                # 如果只有2个尺码，选择较小的那个（避免最大码）
                selected_size = sorted_sizes[0]
                log_message(f"📏 尺码选择策略: 只有2个尺码，选择较小的 {selected_size} (避免最大码 {sorted_sizes[1]})")
                return selected_size
            elif len(sorted_sizes) == 1:
                # 如果只有1个尺码，就选择它
                selected_size = sorted_sizes[0]
                log_message(f"📏 尺码选择策略: 只有1个尺码，选择 {selected_size}")
                return selected_size

    log_message("📏 尺码选择策略: 使用默认尺码 130")
    return 130  # 默认尺码

    print("=" * 60)
    log_message("测试完成！按回车键退出...")
    try:
        input()
    except EOFError:
        log_message("程序在非交互模式下运行，自动退出")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        log_message(f"程序发生错误: {e}")
        import traceback
        print("\n错误详细信息:")
        print(traceback.format_exc())
        print("\n按回车键退出...")
        try:
            input()
        except EOFError:
            print("程序在非交互模式下运行，自动退出")
