; AutoHotkey v2脚本 - 真实模拟Ctrl+V
; 兼容AutoHotkey v1和v2

; 检测AutoHotkey版本
if (A_AhkVersion >= "2.0") {
    ; AutoHotkey v2语法
    ; 等待一小段时间确保窗口焦点正确
    Sleep(500)

    ; 执行Ctrl+V
    Send("^v")

    ; 等待操作完成
    Sleep(1000)

    ; 退出脚本
    ExitApp()
} else {
    ; AutoHotkey v1语法
    #NoEnv
    #SingleInstance Force

    ; 等待一小段时间确保窗口焦点正确
    Sleep, 500

    ; 执行Ctrl+V
    Send, ^v

    ; 等待操作完成
    Sleep, 1000

    ; 退出脚本
    ExitApp
}
