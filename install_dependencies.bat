@echo off
chcp 65001 >nul
echo ================================================================
echo 自动捕获数据项目 - 依赖安装脚本
echo ================================================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到Python环境，请先安装Python 3.8+
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 正在检查pip...
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到pip，请检查Python安装
    pause
    exit /b 1
)

echo ✅ pip检查通过
echo.

echo 开始安装依赖包...
echo 使用清华镜像源加速下载...
echo.

pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host pypi.tuna.tsinghua.edu.cn

if %errorlevel% equ 0 (
    echo.
    echo ================================================================
    echo ✅ 依赖安装完成！
    echo ================================================================
    echo.
    echo 已安装的依赖包：
    echo - pandas==1.5.3          ^(数据处理^)
    echo - openpyxl==3.1.2        ^(Excel操作^)
    echo - xlrd==2.0.1            ^(Excel读取^)
    echo - numpy==1.26.4          ^(数值计算^)
    echo - pyautogui==0.9.54      ^(GUI自动化^)
    echo - pyperclip==1.9.0       ^(剪贴板操作^)
    echo - pywin32==308           ^(Windows API^)
    echo - opencv-python==********* ^(图像处理^)
    echo - pillow==10.2.0         ^(图像库^)
    echo - paddlepaddle==3.0.0    ^(深度学习框架^)
    echo - paddleocr==3.0.1       ^(OCR识别^)
    echo.
    echo 🎉 现在可以运行主程序了：python main.py
    echo.
) else (
    echo.
    echo ================================================================
    echo ❌ 依赖安装失败！
    echo ================================================================
    echo.
    echo 可能的解决方案：
    echo 1. 检查网络连接
    echo 2. 尝试其他镜像源：
    echo    - 阿里云：pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/
    echo    - 豆瓣：  pip install -r requirements.txt -i https://pypi.douban.com/simple/
    echo    - 中科大：pip install -r requirements.txt -i https://pypi.mirrors.ustc.edu.cn/simple/
    echo 3. 升级pip：python -m pip install --upgrade pip
    echo.
)

echo 按任意键退出...
pause >nul
